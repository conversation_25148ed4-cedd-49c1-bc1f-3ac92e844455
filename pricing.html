<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pricing - Bangladesh Medical Solutions</title>
    
    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #054582;
            --secondary-color: #30A8DD;
            --light-bg: #f8f9fa;
            --dark-text: #2c3e50;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--dark-text);
        }
        
        .navbar {
            background: white !important;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand, .navbar-nav .nav-link {
            color: var(--primary-color) !important;
        }

        .navbar-nav .nav-link:hover {
            color: var(--secondary-color) !important;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 5px 25px rgba(0,0,0,0.15);
            border-radius: 8px;
            padding: 0.5rem 0;
        }

        .dropdown-item {
            color: var(--primary-color);
            padding: 0.5rem 1.5rem;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 80px 0 60px;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,0 1000,0 1000,100 0,80"/></svg>');
            background-size: cover;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .section-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 2rem;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: var(--secondary-color);
            border-radius: 2px;
        }
        
        .card {
            border: none;
            box-shadow: 0 5px 25px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 40px rgba(0,0,0,0.12);
        }
        
        .pricing-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 25px rgba(0,0,0,0.08);
        }
        
        .pricing-table table {
            margin-bottom: 0;
        }
        
        .pricing-table th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            font-weight: 600;
            border: none;
            padding: 1rem;
        }
        
        .pricing-table td {
            padding: 0.75rem 1rem;
            border-color: #e9ecef;
            vertical-align: middle;
        }
        
        .service-category {
            background: var(--light-bg);
            color: var(--primary-color);
        }

        .service-category td:first-child {
            font-weight: 600;
        }
        
        .price-highlight {
            color: var(--secondary-color);
            font-weight: 600;
        }
        
        .delivery-time {
            color: #28a745;
            font-weight: 500;
        }
        
        .footer {
            background: var(--primary-color);
            color: white;
            padding: 3rem 0 1rem;
        }
        
        .logo-container {
            max-width: 80px;
            height: auto;
        }
        
        @media (max-width: 768px) {
            .hero-section {
                padding: 60px 0 40px;
            }
            
            .hero-section h1 {
                font-size: 2rem;
            }
            
            .pricing-table {
                font-size: 0.9rem;
            }
            
            .pricing-table th,
            .pricing-table td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="index.html">
                <img src="logo.svg" alt="BMS Logo" class="logo-container me-2">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="index.html">Home</a></li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="aboutDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            About Us
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="index.html#about">About</a></li>
                            <li><a class="dropdown-item" href="index.html#mission">Mission & Vision</a></li>
                            <li><a class="dropdown-item" href="leadership.html">Leadership</a></li>
                            <li><a class="dropdown-item" href="index.html#values">Values</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="servicesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Services
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="what-we-do.html">What We Do</a></li>
                            <li><a class="dropdown-item" href="pricing.html">Pricing</a></li>
                            <li><a class="dropdown-item" href="index.html#why-choose">Why Choose Us</a></li>
                        </ul>
                    </li>
                    <li class="nav-item"><a class="nav-link" href="contact.html">Contact Us</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-12 hero-content text-center">
                    <h1 class="display-4 fw-bold mb-4 mt-4">BMS – Online Radiology Reporting Price List</h1>
                    <p class="lead mb-0">Below is our pricing for digital radiology reporting services. All reports are prepared by licensed radiologists and delivered securely via our platform.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Table Section -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="pricing-table">
                        <table class="table table-striped mb-0">
                            <thead>
                                <tr>
                                    <th>Service Type</th>
                                    <th>Description</th>
                                    <th>Rate (BDT)</th>
                                    <th>Delivery Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="service-category">
                                    <td rowspan="5">🩻 X-Ray</td>
                                    <td>Single View</td>
                                    <td class="price-highlight">30 TK</td>
                                    <td class="delivery-time">15 mins</td>
                                </tr>
                                <tr>
                                    <td>Both Views</td>
                                    <td class="price-highlight">60 TK</td>
                                    <td class="delivery-time">20 mins</td>
                                </tr>
                                <tr>
                                    <td>Contrast (IVU, Barium)</td>
                                    <td class="price-highlight">120 TK</td>
                                    <td class="delivery-time">30 mins</td>
                                </tr>
                                <tr>
                                    <td>Mammography (1 Breast)</td>
                                    <td class="price-highlight">120 TK</td>
                                    <td class="delivery-time">30 mins</td>
                                </tr>
                                <tr>
                                    <td>Mammography (Both)</td>
                                    <td class="price-highlight">240 TK</td>
                                    <td class="delivery-time">30 mins</td>
                                </tr>
                                <tr class="service-category">
                                    <td>📉 ECG</td>
                                    <td>Standard ECG Reporting</td>
                                    <td class="price-highlight">35 TK</td>
                                    <td class="delivery-time">20 mins</td>
                                </tr>
                                <tr class="service-category">
                                    <td rowspan="5">🧠 CT Scan</td>
                                    <td>Brain</td>
                                    <td class="price-highlight">300 TK</td>
                                    <td class="delivery-time">1 hour</td>
                                </tr>
                                <tr>
                                    <td>HRCT/Chest/Pelvis</td>
                                    <td class="price-highlight">500 TK</td>
                                    <td class="delivery-time">1 hour</td>
                                </tr>
                                <tr>
                                    <td>Whole Abdomen</td>
                                    <td class="price-highlight">750 TK</td>
                                    <td class="delivery-time">1 hour</td>
                                </tr>
                                <tr>
                                    <td>Coronary Angiogram</td>
                                    <td class="price-highlight">1000 TK</td>
                                    <td class="delivery-time">2 hours</td>
                                </tr>
                                <tr>
                                    <td>Limb Angiogram</td>
                                    <td class="price-highlight">1200 TK</td>
                                    <td class="delivery-time">2 hours</td>
                                </tr>
                                <tr class="service-category">
                                    <td rowspan="4">🧲 MRI</td>
                                    <td>Brain/Spine</td>
                                    <td class="price-highlight">500 TK</td>
                                    <td class="delivery-time">1 hour</td>
                                </tr>
                                <tr>
                                    <td>Pelvis/Abdomen</td>
                                    <td class="price-highlight">600 TK</td>
                                    <td class="delivery-time">1 hour</td>
                                </tr>
                                <tr>
                                    <td>Whole Abdomen/MRCP</td>
                                    <td class="price-highlight">750 TK</td>
                                    <td class="delivery-time">1 hour</td>
                                </tr>
                                <tr>
                                    <td>MR Angiogram</td>
                                    <td class="price-highlight">1000 TK</td>
                                    <td class="delivery-time">2 hours</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4">
                    <div class="d-flex align-items-center mb-3">
                        <img src="logo_white.svg" alt="BMS Logo" class="logo-container me-2">
                        <h5 class="mb-0">Bangladesh Medical Solutions</h5>
                    </div>
                    <p>Empowering healthcare providers with innovative digital tools and reliable medical equipment across Bangladesh.</p>
                </div>
                <div class="col-lg-4 text-lg-start">
                    <h6 class="mb-3">Head Office</h6>
                    <div class="mb-3">
                        <p class="mb-0"><i class="bi bi-geo-alt me-2"></i>House 4803, Road 02, Block B<br>Fulbariya, Uttara, Dhaka-1710</p>
                    </div>
                    <div>
                         <p class="mb-1"><i class="bi bi-envelope me-2"></i><EMAIL></p>
                    </div>
                </div>
                <div class="col-lg-4 text-lg-start">
                    <h6 class="mb-3">Rajshahi Corporate Office</h6>
                    <div>
                        <p class="mb-0"><i class="bi bi-geo-alt me-2"></i>House 29, Vatapara, Rajpara<br>Rajshahi-6000</p>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0">&copy; 2025 Bangladesh Medical Solutions. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Smooth Scrolling -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar background on scroll
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.backdropFilter = 'blur(10px)';
            } else {
                navbar.style.background = 'white';
                navbar.style.backdropFilter = 'none';
            }
        });
    </script>
</body>
</html>
